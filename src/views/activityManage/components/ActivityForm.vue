<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="100px"
      :disabled="isDetail"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="活动标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入活动标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大人数" prop="activityQuota">
            <el-input-number
              v-model="form.activityQuota"
              :min="1"
              :max="9999"
              placeholder="请输入最大参会人数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="门市价" prop="marketPrice">
            <el-input-number
              v-model="form.marketPrice"
              :min="0"
              :precision="2"
              placeholder="请输入门市价"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="会员价" prop="memberPrice">
            <el-input-number
              v-model="form.memberPrice"
              :min="0"
              :precision="2"
              placeholder="请输入会员价"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="非会员价" prop="nonMemberPrice">
            <el-input-number
              v-model="form.nonMemberPrice"
              :min="0"
              :precision="2"
              placeholder="请输入非会员价"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- 空列，保持布局平衡 -->
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="活动地点" prop="location">
            <div style="display: flex; gap: 10px; width: 100%">
              <el-cascader
                v-model="regionValue"
                :options="regionOptions"
                :props="cascaderProps"
                placeholder="请选择省市区"
                style="flex: 1"
                clearable
                @change="handleRegionChange"
              />
              <el-input
                v-model="form.detailAddress"
                placeholder="请输入详细地址"
                style="flex: 2"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="活动时间" prop="timeRange">
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
              style="width: 100%"
              @change="handleTimeChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="活动图片" prop="activityImage">
            <image-upload
              v-model="form.activityImage"
              :limit="5"
              :file-size="5"
              :file-type="['jpg', 'jpeg', 'png', 'gif']"
              :disabled="isDetail"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="活动内容" prop="activityDetails">
            <el-input
              v-model="form.activityDetails"
              type="textarea"
              :rows="4"
              placeholder="请输入活动内容描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          v-if="!isDetail"
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { nextTick } from "vue";
import {
  activityAdd,
  activityUpdate,
  activityDetail as getActivityDetail,
} from "@/api/activityManage/index";
import regionData from "@/utils/region.js";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "add", // add, edit, detail
  },
  activityId: {
    type: [String, Number],
    default: null,
  },
});

const emit = defineEmits(["update:visible", "success"]);
const { proxy } = getCurrentInstance();

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

const dialogTitle = computed(() => {
  const titleMap = {
    add: "新增活动",
    edit: "编辑活动",
    detail: "活动详情",
  };
  return titleMap[props.type] || "活动信息";
});

const isDetail = computed(() => props.type === "detail");

const submitLoading = ref(false);
const formRef = ref();
const timeRange = ref([]);
const regionValue = ref([]);

// 表单数据
const form = ref({
  id: null,
  title: "",
  activityQuota: null,
  marketPrice: null,
  memberPrice: null,
  nonMemberPrice: null,
  location: null, // 改为对象格式
  locationCode: "", // 添加locationCode字段
  detailAddress: "",
  startTime: "",
  endTime: "",
  activityImage: "",
  activityDetails: "",
  remark: "",
  timeRange: [], // 添加timeRange字段用于验证
});

// 表单验证规则
const rules = {
  title: [
    { required: true, message: "请输入活动标题", trigger: "blur" },
    {
      min: 2,
      max: 100,
      message: "活动标题长度在 2 到 100 个字符",
      trigger: "blur",
    },
  ],
  activityQuota: [
    { required: true, message: "请输入最大参会人数", trigger: "blur" },
    {
      type: "number",
      min: 1,
      message: "最大参会人数不能小于1",
      trigger: "blur",
    },
  ],
  marketPrice: [
    { required: true, message: "请输入门市价", trigger: "blur" },
    { type: "number", min: 0, message: "门市价不能小于0", trigger: "blur" },
  ],
  memberPrice: [
    { required: true, message: "请输入会员价", trigger: "blur" },
    { type: "number", min: 0, message: "会员价不能小于0", trigger: "blur" },
  ],
  nonMemberPrice: [
    { required: true, message: "请输入非会员价", trigger: "blur" },
    { type: "number", min: 0, message: "非会员价不能小于0", trigger: "blur" },
  ],
  location: [
    {
      required: true,
      validator: (_rule, value, callback) => {
        if (!value || !value.locationCode) {
          callback(new Error("请选择活动地点"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  timeRange: [
    {
      required: true,
      validator: (_rule, value, callback) => {
        if (!value || value.length !== 2 || !value[0] || !value[1]) {
          callback(new Error("请选择活动时间"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
};

// 省市区级联选择器配置
const cascaderProps = {
  value: "value",
  label: "label",
  children: "children",
  emitPath: true,
};

// 省市区数据
const regionOptions = ref(regionData.result || []);

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    if (val) {
      resetForm();
      if (props.type !== "add" && props.activityId) {
        loadActivityDetail();
      }
    }
  }
);

// 重置表单
function resetForm() {
  form.value = {
    id: null,
    title: "",
    activityQuota: null,
    marketPrice: null,
    memberPrice: null,
    nonMemberPrice: null,
    location: null,
    locationCode: "",
    detailAddress: "",
    startTime: "",
    endTime: "",
    activityImage: "",
    activityDetails: "",
    remark: "",
    timeRange: [],
  };
  timeRange.value = [];
  regionValue.value = [];
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 加载活动详情
async function loadActivityDetail() {
  try {
    const res = await getActivityDetail(props.activityId);
    const data = res.data;

    // 先填充非地址类字段
    form.value = {
      ...form.value,
      id: data.id,
      title: data.title || "",
      activityQuota: data.activityQuota,
      marketPrice: data.marketPrice,
      memberPrice: data.memberPrice,
      nonMemberPrice: data.nonMemberPrice,
      startTime: data.startTime,
      endTime: data.endTime,
      activityImage: data.activityImage || "",
      activityDetails: data.activityDetails || "",
      remark: data.remark || "",
    };

    // 处理地址回显
    if (data.locationCode) {
      const locationCodes = data.locationCode.split("，");
      regionValue.value = locationCodes; // 回显省市区选择器
      form.value.locationCode = data.locationCode;

      const locationNames = locationCodes
        .map((code) => {
          const region = findRegionByValue(code);
          return region ? region.label : "";
        })
        .filter(Boolean);

      if (locationNames.length > 0) {
        const regionNameStr = locationNames.join("");
        // 设置内部 location 对象，用于校验
        form.value.location = {
          locationName: regionNameStr,
          locationCode: data.locationCode,
        };

        // 从完整地址中提取详细地址
        if (data.location) {
          const detail = data.location.replace(regionNameStr, "").trim();
          form.value.detailAddress = detail;
        }
      }
    } else if (data.location) {
      // 兼容没有 locationCode 的情况
      form.value.detailAddress = data.location;
    }

    // 设置时间范围
    if (data.startTime && data.endTime) {
      const timeRangeVal = [data.startTime, data.endTime];
      timeRange.value = timeRangeVal;
      form.value.timeRange = timeRangeVal;
    }
  } catch (error) {
    proxy.$modal.msgError("获取活动详情失败");
  }
}

// 处理地区选择变化
function handleRegionChange(value) {
  updateLocation();
  // 触发表单验证
  nextTick(() => {
    if (formRef.value) {
      formRef.value.validateField("location");
    }
  });
}

// 处理时间范围变化
function handleTimeChange(value) {
  // 更新timeRange用于验证
  form.value.timeRange = value || [];

  if (value && value.length === 2) {
    form.value.startTime = value[0];
    form.value.endTime = value[1];
  } else {
    form.value.startTime = "";
    form.value.endTime = "";
  }

  // 触发表单验证
  nextTick(() => {
    if (formRef.value) {
      formRef.value.validateField("timeRange");
    }
  });
}

// 根据value查找地区名称
function findRegionByValue(value, regions = regionOptions.value) {
  for (const region of regions) {
    if (region.value === value) {
      return region;
    }
    if (region.children) {
      const found = findRegionByValue(value, region.children);
      if (found) return found;
    }
  }
  return null;
}

// 更新完整地址
function updateLocation() {
  if (!regionValue.value || regionValue.value.length === 0) {
    form.value.location = null;
    form.value.locationCode = "";
    return;
  }

  const locationNames = [];
  const locationValues = [];

  // 遍历选中的地区value，获取对应的名称
  for (const value of regionValue.value) {
    const region = findRegionByValue(value);
    if (region) {
      locationNames.push(region.label);
      locationValues.push(region.value);
    }
  }

  // 构建location对象（内部使用）
  form.value.location = {
    locationName: locationNames.join(""),
    locationCode: locationValues.join("，"),
  };

  // 设置locationCode字段（提交给后端，使用value）
  form.value.locationCode = locationValues.join("，");
}

// 监听详细地址变化
watch(
  () => form.value.detailAddress,
  () => {
    // 详细地址变化时不需要更新location对象，只在省市区变化时更新
  }
);

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate();

    submitLoading.value = true;

    const submitData = { ...form.value };

    // 删除timeRange字段，不提交到后端
    delete submitData.timeRange;

    // 格式化时间, 去掉秒
    if (submitData.startTime) {
      submitData.startTime = submitData.startTime.substring(0, 16);
    }
    if (submitData.endTime) {
      submitData.endTime = submitData.endTime.substring(0, 16);
    }

    // 格式化location为字符串
    if (submitData.location && typeof submitData.location === "object") {
      // 将location对象转换为字符串格式
      let locationStr = submitData.location.locationName || "";
      if (submitData.detailAddress) {
        locationStr += " " + submitData.detailAddress;
      }
      submitData.location = locationStr.trim();
    }

    if (props.type === "add") {
      // 新增时删除id和locationCode字段
      delete submitData.id;
      delete submitData.locationCode;
    } else {
      // 编辑时确保locationCode字段存在
      if (!submitData.locationCode) {
        submitData.locationCode = "";
      }
    }

    if (props.type === "add") {
      await activityAdd(submitData);
      proxy.$modal.msgSuccess("新增成功");
    } else if (props.type === "edit") {
      await activityUpdate(submitData);
      proxy.$modal.msgSuccess("修改成功");
    }

    emit("success");
    handleClose();
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    submitLoading.value = false;
  }
}

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false;
  resetForm();
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>