<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="活动标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入活动标题"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动编号" prop="activityNo">
        <el-input
          v-model="queryParams.activityNo"
          placeholder="请输入活动编号"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="活动状态"
          clearable
          style="width: 240px"
        >
          <el-option label="待进行" value="1" />
          <el-option label="进行中" value="2" />
          <el-option label="已完成" value="3" />
          <el-option label="已取消" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px;">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="activityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="活动编号" align="center" prop="activityNo" v-if="columns[0].visible" width="160" />
      <el-table-column label="活动标题" align="center" min-width="140" prop="title" v-if="columns[1].visible" :show-overflow-tooltip="true" />
      <el-table-column label="活动图片" align="center" prop="activityImage" v-if="columns[2].visible" width="100">
        <template #default="scope">
          <el-image
            v-if="scope.row.activityImage"
            :src="scope.row.activityImage"
            :preview-src-list="[scope.row.activityImage]"
            fit="cover"
            style="width: 60px; height: 40px; border-radius: 4px;"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="活动地点" align="center" prop="location" v-if="columns[3].visible" :show-overflow-tooltip="true" />
      <el-table-column label="活动时间" align="center" v-if="columns[4].visible" min-width="280">
        <template #default="scope">
          <div>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}~{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</div>
        </template>
      </el-table-column>
      <el-table-column label="报名情况" align="center" v-if="columns[5].visible" width="120">
        <template #default="scope">
          <el-tag type="info">{{ scope.row.attendanceCount || 0 }}/{{ scope.row.activityQuota || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="门市价" align="center" v-if="columns[6].visible" width="150">
        <template #default="scope">
          <div> ¥{{ scope.row.marketPrice || 0 }}</div>
        </template>
      </el-table-column>
       <el-table-column label="会员价" align="center" v-if="columns[7].visible" width="150">
        <template #default="scope">
          <div>¥{{ scope.row.memberPrice || 0 }}</div>
        </template>
      </el-table-column>
       <el-table-column label="非会员价" align="center" v-if="columns[8].visible" width="150">
        <template #default="scope">
          <div>¥{{ scope.row.nonMemberPrice || 0 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="活动状态" align="center" prop="status" v-if="columns[9].visible" width="100">
        <template #default="scope">
          <el-tag
            :type="getStatusTagType(scope.row.status)"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[10].visible" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width" fixed="right">
        <template #default="scope">
          <el-tooltip content="详情" placement="top">
            <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="编辑" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="取消活动" placement="top" v-if="scope.row.status === 1 || scope.row.status === 2">
            <el-button link type="primary" icon="Close" @click="handleCancel(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 活动表单弹窗 -->
    <ActivityForm
      v-model:visible="formVisible"
      :type="formType"
      :activity-id="currentActivityId"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup name="ActivityManage">
import { activityPage, activityDelete, activityCancel } from "@/api/activityManage/index";
import ActivityForm from './components/ActivityForm.vue';

const { proxy } = getCurrentInstance();

const activityList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);

// 表单弹窗相关
const formVisible = ref(false);
const formType = ref('add'); // add, edit, detail
const currentActivityId = ref(null);

// 列显隐信息
const columns = ref([
  { key: 0, label: `活动编号`, visible: true },
  { key: 1, label: `活动标题`, visible: true },
  { key: 2, label: `活动图片`, visible: true },
  { key: 3, label: `活动地点`, visible: true },
  { key: 4, label: `活动时间`, visible: true },
  { key: 5, label: `报名情况`, visible: true },
  { key: 6, label: `门市价`, visible: true },
  { key: 7, label: `会员价`, visible: true },
  { key: 8, label: `非会员价`, visible: true },
  { key: 9, label: `活动状态`, visible: true },
  { key: 10, label: `创建时间`, visible: true }
]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    activityNo: undefined,
    status: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询活动列表 */
function getList() {
  loading.value = true;
  const params = proxy.addDateRange(queryParams.value, dateRange.value);
  activityPage(params).then(res => {
    loading.value = false;
    activityList.value = res.data.rows||[];
    total.value = res.total || 0;
  }).catch(() => {
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  console.log('点击新增按钮');
  formType.value = 'add';
  currentActivityId.value = null;
  formVisible.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const activityId = row?.id || ids.value[0];
  console.log('点击修改按钮，活动ID:', activityId);
  formType.value = 'edit';
  currentActivityId.value = activityId;
  formVisible.value = true;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const activityIds = row?.id || ids.value;
  console.log('点击删除按钮，活动ID:', activityIds);
  
  proxy.$modal.confirm('是否确认删除选中的活动数据项？').then(function () {
    return activityDelete(Array.isArray(activityIds) ? activityIds : [activityIds]);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 详情按钮操作 */
function handleDetail(row) {
  console.log('点击详情按钮，活动数据:', row);
  formType.value = 'detail';
  currentActivityId.value = row.id;
  formVisible.value = true;
}

/** 表单提交成功回调 */
function handleFormSuccess() {
  getList();
}

/** 取消活动按钮操作 */
function handleCancel(row) {
  console.log('点击取消活动按钮，活动ID:', row.id, '活动数据:', row);
  
  proxy.$modal.confirm(`是否确认取消活动"${row.title}"？`).then(function () {
    return activityCancel(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("取消活动成功");
  }).catch(() => {});
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 获取状态标签类型 */
function getStatusTagType(status) {
  const statusMap = {
    1: 'warning',  // 待进行
    2: 'success',  // 进行中
    3: 'info',     // 已完成
    4: 'danger'    // 已取消
  };
  return statusMap[status] || 'info';
}

/** 获取状态文本 */
function getStatusText(status) {
  const statusMap = {
    1: '待进行',
    2: '进行中',
    3: '已完成',
    4: '已取消'
  };
  return statusMap[status] || '未知';
}

// 初始化
onMounted(() => {
  getList();
});
</script>