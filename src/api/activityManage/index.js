import request from '@/utils/request'

// 活动新增
export const activityAdd = (data) => {
  return request({
    url: '/activity/add',
    method: 'post',
    data: data,
  })
}

// 活动编辑
export const activityUpdate = (data) => {
  return request({
    url: '/activity/update',
    method: 'post',
    data: data,
  })
}

// 活动删除
export const activityDelete = (data) => {
  return request({
    url: '/activity/delete',
    method: 'post',
    data: data,
  })
}

// 活动详情
export const activityDetail = (detailId) => {
  return request({
    url: '/activity/detail' + '?id=' + (String(detailId)),
    method: 'get'
  })
}


// 活动列表
export const activityPage = (data) => {
  return request({
    url: '/activity/page',
    method: 'post',
    data: data,
  })
}


// 活动取消
export const activityCancel = (detailId) => {
  return request({
    url: '/activity/cancel' + '?id=' + encodeURIComponent(String(detailId)),
    method: 'get'
  })
}
